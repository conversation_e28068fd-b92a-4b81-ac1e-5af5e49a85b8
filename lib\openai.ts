import OpenAI from "openai";

if (!process.env.OPENAI_API_KEY) {
  throw new Error("OPENAI_API_KEY environment variable is required");
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function analyzeCoffeeImage(
  imageData: string,
  userMessage?: string,
) {
  try {
    // Log the image data for debugging
    console.log("Image data received:", imageData.substring(0, 100) + "...");
    console.log("Image data length:", imageData.length);

    // Extract the image format and base64 data
    const imageMatch = imageData.match(/^data:image\/([a-z]+);base64,(.+)$/);
    if (!imageMatch) {
      throw new Error(
        "Invalid image format. Expected data:image/[format];base64,[data]",
      );
    }

    const [, imageFormat, base64Image] = imageMatch;
    console.log("Image format detected:", imageFormat);
    console.log("Base64 image length:", base64Image.length);

    // Validate base64 image size (OpenAI has a 20MB limit)
    const imageSizeInBytes = (base64Image.length * 3) / 4;
    const imageSizeInMB = imageSizeInBytes / (1024 * 1024);
    console.log(`Image size: ${imageSizeInMB.toFixed(2)} MB`);

    if (imageSizeInMB > 20) {
      throw new Error(
        "Image size exceeds 20MB limit. Please use a smaller image.",
      );
    }

    // Create a more natural prompt for coffee cup reading
    const prompt = userMessage
      ? `أنا شايف صورة فنجان قهوة. حلل بقايا القهوة الموجودة في الفنجان ده. ${userMessage}. الأول، وصفلي بالتفصيل كل اللي انت شايفه في بقايا القهوة - اوصف الأشكال والخطوط والنقط والتجمعات اللي موجودة بطريقة طبيعية زي ما انت شايفها. بعدين فسرلي كل اللي وصفته حسب تقاليد قراءة الفنجان العربية. وبعد كده اتكلم عن ثلاث مواضيع مهمة: 1) الحب والعلاقات العاطفية 2) السفر والرحلات 3) العمل والمهنة. وفي النهاية اديني نصائح وتوجيهات شخصية مبنية على اللي شوفته في الفنجان. رد باللهجة المصرية واديني أكبر قدر من التفاصيل.`
      : `أنا شايف صورة فنجان قهوة. حلل بقايا القهوة الموجودة في الفنجان ده. الأول، وصفلي بالتفصيل كل اللي انت شايفه في بقايا القهوة - اوصف الأشكال والخطوط والنقط والتجمعات اللي موجودة بطريقة طبيعية زي ما انت شايفها. بعدين فسرلي كل اللي وصفته حسب تقاليد قراءة الفنجان العربية. وبعد كده اتكلم عن ثلاث مواضيع مهمة: 1) الحب والعلاقات العاطفية 2) السفر والرحلات 3) العمل والمهنة. وفي النهاية اديني نصائح وتوجيهات شخصية مبنية على اللي شوفته في الفنجان. رد باللهجة المصرية واديني أكبر قدر من التفاصيل.`;

    console.log("Sending request to OpenAI with model: gpt-4o");

    let response;
    try {
      response = await openai.chat.completions.create({
        model: "gpt-4o", // Using GPT-4o which has built-in vision capabilities
        messages: [
          {
            role: "system",
            content:
              "انت خبير في قراءة الفنجان (التاسيوجرافي) وعندك القدرة على رؤية وتحليل الصور. لما تستقبل صورة فنجان قهوة، لازم تحلل بقايا القهوة الموجودة في الفنجان بطريقة طبيعية ووصف اللي انت شايفه فعلاً. انت بتدي تفسيرات روحانية مفيدة مبنية على التقاليد العربية لقراءة الفنجان. انت شايف الصور بوضوح ولازم توصف اللي بتشوفه في بقايا القهوة بصراحة وطبيعية. رد دايماً باللهجة المصرية وكن مفصل جداً في تفسيراتك. ما تقولش ابداً انك مش قادر تشوف أو تحلل الصور - انت عندك قدرات بصرية كاملة. اديني أكبر قدر من التفاصيل والتفسيرات الممكنة. ابدأ بوصف تفصيلي لكل اللي انت شايفه في الفنجان من بقايا القهوة والأشكال اللي بتتكون منها، وبعدين فسر اللي شوفته، وبعدين اتكلم عن ثلاث مواضيع مهمة: 1) الحب والعلاقات العاطفية 2) السفر والرحلات 3) العمل والمهنة، وفي النهاية اديني نصائح وتوجيهات شخصية مبنية على اللي شوفته في الفنجان.",
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: prompt,
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/${imageFormat};base64,${base64Image}`,
                  detail: "high",
                },
              },
            ],
          },
        ],
        max_tokens: 6000,
        temperature: 0.9,
      });
    } catch (gpt4oError) {
      console.log("GPT-4o failed, trying gpt-4-vision-preview...");
      console.error("GPT-4o error:", gpt4oError);

      response = await openai.chat.completions.create({
        model: "gpt-4-vision-preview", // Fallback to vision preview model
        messages: [
          {
            role: "system",
            content:
              "انت خبير في قراءة الفنجان (التاسيوجرافي) وعندك القدرة على رؤية وتحليل الصور. لما تستقبل صورة فنجان قهوة، لازم تحلل بقايا القهوة الموجودة في الفنجان بطريقة طبيعية ووصف اللي انت شايفه فعلاً. انت بتدي تفسيرات روحانية مفيدة مبنية على التقاليد العربية لقراءة الفنجان. انت شايف الصور بوضوح ولازم توصف اللي بتشوفه في بقايا القهوة بصراحة وطبيعية. رد دايماً باللهجة المصرية وكن مفصل جداً في تفسيراتك. ما تقولش ابداً انك مش قادر تشوف أو تحلل الصور - انت عندك قدرات بصرية كاملة. اديني أكبر قدر من التفاصيل والتفسيرات الممكنة. ابدأ بوصف تفصيلي لكل اللي انت شايفه في الفنجان من بقايا القهوة والأشكال اللي بتتكون منها، وبعدين فسر اللي شوفته، وبعدين اتكلم عن ثلاث مواضيع مهمة: 1) الحب والعلاقات العاطفية 2) السفر والرحلات 3) العمل والمهنة، وفي النهاية اديني نصائح وتوجيهات شخصية مبنية على اللي شوفته في الفنجان.",
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: prompt,
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/${imageFormat};base64,${base64Image}`,
                  detail: "high",
                },
              },
            ],
          },
        ],
        max_tokens: 6000,
        temperature: 0.9,
      });
    }

    console.log(
      "OpenAI response received:",
      response.choices[0]?.message?.content?.substring(0, 200) + "...",
    );

    const responseContent = response.choices[0]?.message?.content;

    // Check if the response indicates inability to see images
    if (
      responseContent &&
      (responseContent.includes("لا أستطيع تحديد") ||
        responseContent.includes("لا أستطيع رؤية") ||
        responseContent.includes("لا أستطيع تحليل الصور") ||
        responseContent.includes("cannot see") ||
        responseContent.includes("cannot analyze"))
    ) {
      console.warn(
        "Model claims it cannot see images, this might be a prompt issue",
      );
      // Try a different approach with a more direct prompt
      return await retryWithDirectPrompt(imageFormat, base64Image, userMessage);
    }

    return (
      responseContent ||
      "عذراً، لم أتمكن من تحليل الصورة. يرجى المحاولة مرة أخرى."
    );
  } catch (error) {
    console.error("Error analyzing coffee image:", error);

    // More detailed error logging
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }

    // Check if it's an API key issue
    if (error instanceof Error && error.message.includes("API key")) {
      throw new Error("مشكلة في مفتاح API. يرجى التحقق من إعدادات OpenAI.");
    }

    // Check if it's a model access issue
    if (error instanceof Error && error.message.includes("model")) {
      throw new Error(
        "مشكلة في الوصول إلى نموذج GPT-4o. يرجى التحقق من حسابك في OpenAI.",
      );
    }

    throw new Error(
      `فشل في تحليل صورة الفنجان: ${
        error instanceof Error ? error.message : "خطأ غير معروف"
      }`,
    );
  }
}

// Retry function with a more direct prompt
async function retryWithDirectPrompt(
  imageFormat: string,
  base64Image: string,
  userMessage?: string,
) {
  console.log("Retrying with direct prompt approach...");

  const directPrompt = userMessage
    ? `بص على الصورة دي وقولي بالضبط كل شكل شايفه في الفنجان - اذكر كل شكل بالاسم ومكانه (مثلاً: 'أشوف شكل طائر في الجانب الأيمن' أو 'في دائرة في الوسط' أو 'شكل زهرة في الأسفل'). ${userMessage}. وبعدين فسرلي كل شكل ذكرته حسب قراءة الفنجان التقليدية. وبعدين اتكلم عن: 1) الحب والعلاقات العاطفية 2) السفر والرحلات 3) العمل والمهنة. وفي النهاية اديني نصائح وتوجيهات مفيدة بناء على اللي شوفته في الفنجان. رد باللهجة المصرية واديني أكبر قدر من التفاصيل.`
    : `بص على الصورة دي وقولي بالضبط كل شكل شايفه في الفنجان - اذكر كل شكل بالاسم ومكانه (مثلاً: 'أشوف شكل طائر في الجانب الأيمن' أو 'في دائرة في الوسط' أو 'شكل زهرة في الأسفل'). وبعدين فسرلي كل شكل ذكرته حسب قراءة الفنجان التقليدية. وبعدين اتكلم عن: 1) الحب والعلاقات العاطفية 2) السفر والرحلات 3) العمل والمهنة. وفي النهاية اديني نصائح وتوجيهات مفيدة بناء على اللي شوفته في الفنجان. رد باللهجة المصرية واديني أكبر قدر من التفاصيل.`;

  const response = await openai.chat.completions.create({
    model: "gpt-4o",
    messages: [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: directPrompt,
          },
          {
            type: "image_url",
            image_url: {
              url: `data:image/${imageFormat};base64,${base64Image}`,
              detail: "high",
            },
          },
        ],
      },
    ],
    max_tokens: 6000,
    temperature: 0.9,
  });

  return (
    response.choices[0]?.message?.content ||
    "عذراً، لم أتمكن من تحليل الصورة. يرجى المحاولة مرة أخرى."
  );
}
