import OpenAI from "openai";

if (!process.env.OPENAI_API_KEY) {
  throw new Error("OPENAI_API_KEY environment variable is required");
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function analyzeCoffeeImage(
  imageData: string,
  userMessage?: string,
) {
  try {
    // Log the image data for debugging
    console.log("Image data received:", imageData.substring(0, 100) + "...");
    console.log("Image data length:", imageData.length);

    // Extract the image format and base64 data
    const imageMatch = imageData.match(/^data:image\/([a-z]+);base64,(.+)$/);
    if (!imageMatch) {
      throw new Error(
        "Invalid image format. Expected data:image/[format];base64,[data]",
      );
    }

    const [, imageFormat, base64Image] = imageMatch;
    console.log("Image format detected:", imageFormat);
    console.log("Base64 image length:", base64Image.length);

    // Validate base64 image size (OpenAI has a 20MB limit)
    const imageSizeInBytes = (base64Image.length * 3) / 4;
    const imageSizeInMB = imageSizeInBytes / (1024 * 1024);
    console.log(`Image size: ${imageSizeInMB.toFixed(2)} MB`);

    if (imageSizeInMB > 20) {
      throw new Error(
        "Image size exceeds 20MB limit. Please use a smaller image.",
      );
    }

    console.log("Sending request to OpenAI with model: gpt-4o");

    let response;
    try {
      response = await openai.chat.completions.create({
        model: "gpt-4o", // Using GPT-4o which has built-in vision capabilities
        messages: [
          {
            role: "system",
            content:
              "انت خبير في قراءة الفنجان وتحليل الصور 🔮☕. مهمتك هي تحليل صور فناجين القهوة ووصف ما تراه فيها بتفاصيل كتيرة جداً وبطريقة روحانية عميقة. اكتب ردود طويلة ومفصلة جداً مليانة بالتفاصيل والتفسيرات الروحانية. استخدم الإيموجي كتير في ردودك عشان تخليها أحلى وأكتر تعبير 💫✨. رد دائماً باللهجة المصرية واديني أكبر قدر من التفاصيل والتفسيرات الممكنة. اتكلم عن كل شكل تشوفه بالتفصيل وفسره روحانياً 🌟💖.",
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: userMessage || "حلل الفنجان ده",
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/${imageFormat};base64,${base64Image}`,
                  detail: "high",
                },
              },
            ],
          },
        ],
        max_tokens: 6000,
        temperature: 0.9,
      });
    } catch (gpt4oError) {
      console.log("GPT-4o failed, trying gpt-4-vision-preview...");
      console.error("GPT-4o error:", gpt4oError);

      response = await openai.chat.completions.create({
        model: "gpt-4-vision-preview", // Fallback to vision preview model
        messages: [
          {
            role: "system",
            content:
              "انت خبير في قراءة الفنجان وتحليل الصور 🔮☕. مهمتك هي تحليل صور فناجين القهوة ووصف ما تراه فيها بتفاصيل كتيرة جداً وبطريقة روحانية عميقة. اكتب ردود طويلة ومفصلة جداً مليانة بالتفاصيل والتفسيرات الروحانية. استخدم الإيموجي كتير في ردودك عشان تخليها أحلى وأكتر تعبير 💫✨. رد دائماً باللهجة المصرية واديني أكبر قدر من التفاصيل والتفسيرات الممكنة. اتكلم عن كل شكل تشوفه بالتفصيل وفسره روحانياً 🌟💖.",
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: userMessage || "حلل الفنجان ده",
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/${imageFormat};base64,${base64Image}`,
                  detail: "high",
                },
              },
            ],
          },
        ],
        max_tokens: 6000,
        temperature: 0.9,
      });
    }

    console.log(
      "OpenAI response received:",
      response.choices[0]?.message?.content?.substring(0, 200) + "...",
    );

    const responseContent = response.choices[0]?.message?.content;

    // Check if the response indicates inability to see images
    if (
      responseContent &&
      (responseContent.includes("لا أستطيع تحديد") ||
        responseContent.includes("لا أستطيع رؤية") ||
        responseContent.includes("لا أستطيع تحليل الصور") ||
        responseContent.includes("cannot see") ||
        responseContent.includes("cannot analyze"))
    ) {
      console.warn(
        "Model claims it cannot see images, this might be a prompt issue",
      );
      // Try a different approach with a more direct prompt
      return await retryWithDirectPrompt(imageFormat, base64Image, userMessage);
    }

    return (
      responseContent ||
      "عذراً، لم أتمكن من تحليل الصورة. يرجى المحاولة مرة أخرى."
    );
  } catch (error) {
    console.error("Error analyzing coffee image:", error);

    // More detailed error logging
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }

    // Check if it's an API key issue
    if (error instanceof Error && error.message.includes("API key")) {
      throw new Error("مشكلة في مفتاح API. يرجى التحقق من إعدادات OpenAI.");
    }

    // Check if it's a model access issue
    if (error instanceof Error && error.message.includes("model")) {
      throw new Error(
        "مشكلة في الوصول إلى نموذج GPT-4o. يرجى التحقق من حسابك في OpenAI.",
      );
    }

    throw new Error(
      `فشل في تحليل صورة الفنجان: ${
        error instanceof Error ? error.message : "خطأ غير معروف"
      }`,
    );
  }
}

// Retry function with a more direct prompt
async function retryWithDirectPrompt(
  imageFormat: string,
  base64Image: string,
  userMessage?: string,
) {
  console.log("Retrying with direct prompt approach...");

  const response = await openai.chat.completions.create({
    model: "gpt-4o",
    messages: [
      {
        role: "system",
        content:
          "انت خبير في قراءة الفنجان وتحليل الصور 🔮☕. مهمتك هي تحليل صور فناجين القهوة ووصف ما تراه فيها بتفاصيل كتيرة جداً وبطريقة روحانية عميقة. اكتب ردود طويلة ومفصلة جداً مليانة بالتفاصيل والتفسيرات الروحانية. استخدم الإيموجي كتير في ردودك عشان تخليها أحلى وأكتر تعبير 💫✨. رد دائماً باللهجة المصرية واديني أكبر قدر من التفاصيل والتفسيرات الممكنة. اتكلم عن كل شكل تشوفه بالتفصيل وفسره روحانياً 🌟💖.",
      },
      {
        role: "user",
        content: [
          {
            type: "text",
            text: userMessage || "حلل الفنجان ده",
          },
          {
            type: "image_url",
            image_url: {
              url: `data:image/${imageFormat};base64,${base64Image}`,
              detail: "high",
            },
          },
        ],
      },
    ],
    max_tokens: 6000,
    temperature: 0.9,
  });

  return (
    response.choices[0]?.message?.content ||
    "عذراً، لم أتمكن من تحليل الصورة. يرجى المحاولة مرة أخرى."
  );
}
