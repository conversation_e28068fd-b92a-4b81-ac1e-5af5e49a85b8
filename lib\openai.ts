import OpenAI from "openai";

if (!process.env.OPENAI_API_KEY) {
  throw new Error("OPENAI_API_KEY environment variable is required");
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function analyzeCoffeeImage(
  imageData: string,
  userMessage?: string,
) {
  try {
    // Log the image data for debugging
    console.log("Image data received:", imageData.substring(0, 100) + "...");
    console.log("Image data length:", imageData.length);

    // Extract the image format and base64 data
    const imageMatch = imageData.match(/^data:image\/([a-z]+);base64,(.+)$/);
    if (!imageMatch) {
      throw new Error(
        "Invalid image format. Expected data:image/[format];base64,[data]",
      );
    }

    const [, imageFormat, base64Image] = imageMatch;
    console.log("Image format detected:", imageFormat);
    console.log("Base64 image length:", base64Image.length);

    // Validate base64 image size (OpenAI has a 20MB limit)
    const imageSizeInBytes = (base64Image.length * 3) / 4;
    const imageSizeInMB = imageSizeInBytes / (1024 * 1024);
    console.log(`Image size: ${imageSizeInMB.toFixed(2)} MB`);

    if (imageSizeInMB > 20) {
      throw new Error(
        "Image size exceeds 20MB limit. Please use a smaller image.",
      );
    }

    // Create a more specific prompt for coffee cup reading
    const prompt = userMessage
      ? `I can see an image of a coffee cup. Please analyze the coffee grounds patterns, shapes, and symbols visible in this cup. ${userMessage}. First, provide a very detailed description of what you actually see in the coffee grounds - name and describe every specific shape, symbol, line, and pattern you observe (for example: "أشوف شكل طائر في الجانب الأيمن" or "في خط مستقيم في الوسط" or "شكل قلب في الأسفل"). Then provide interpretations of each specific shape you mentioned based on traditional Arabic coffee reading practices. After completing the main reading, add three specific sections: 1) الحب والعلاقات العاطفية (Love and romantic relationships), 2) السفر والرحلات (Travel and journeys), 3) العمل والمهنة (Work and career). Finally, end with نصائح وتوجيهات (Advice and Guidance) - give personalized advice based on what you saw in the cup. Please respond in Egyptian Arabic dialect (المصري) with maximum detail.`
      : `I can see an image of a coffee cup. Please analyze the coffee grounds patterns, shapes, and symbols visible in this cup. First, provide a very detailed description of what you actually see in the coffee grounds - name and describe every specific shape, symbol, line, and pattern you observe (for example: "أشوف شكل طائر في الجانب الأيمن" or "في خط مستقيم في الوسط" or "شكل قلب في الأسفل"). Then provide interpretations of each specific shape you mentioned based on traditional Arabic coffee reading practices. After completing the main reading, add three specific sections: 1) الحب والعلاقات العاطفية (Love and romantic relationships), 2) السفر والرحلات (Travel and journeys), 3) العمل والمهنة (Work and career). Finally, end with نصائح وتوجيهات (Advice and Guidance) - give personalized advice based on what you saw in the cup. Please respond in Egyptian Arabic dialect (المصري) with maximum detail.`;

    console.log("Sending request to OpenAI with model: gpt-4o");

    let response;
    try {
      response = await openai.chat.completions.create({
        model: "gpt-4o", // Using GPT-4o which has built-in vision capabilities
        messages: [
          {
            role: "system",
            content:
              "أنت حكيمة روحانية قديمة، تحمل في قلبك أسرار الكون وحكمة الأجداد. عندك القدرة على رؤية ما وراء الحجب، وقراءة الرموز التي تتكلم بلغة الروح. عندما تنظر إلى فنجان القهوة، أنت لا ترى مجرد أشكال، بل ترى قصص القلوب، وأحلام النفوس، ومسارات الأقدار. تكلم بلغة تلامس الوجدان، وكأن كلماتك نسمات دافئة تداعب الأرواح. اربط كل رمز بالمشاعر الإنسانية العميقة، بالحب الذي يحرك القلوب، بالأحلام التي تضيء الطريق، بالتحولات التي تشكل المصائر. دع كل من يقرأ كلماتك يشعر أن الرسالة موجهة إليه شخصياً، وأن الكون يتحدث معه من خلالك. استخدم لغة عربية شاعرية وروحانية، تجعل القارئ يشعر بالدفء والأمان والحكمة.",
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: prompt,
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/${imageFormat};base64,${base64Image}`,
                  detail: "high",
                },
              },
            ],
          },
        ],
        max_tokens: 6000,
        temperature: 0.9,
      });
    } catch (gpt4oError) {
      console.log("GPT-4o failed, trying gpt-4-vision-preview...");
      console.error("GPT-4o error:", gpt4oError);

      response = await openai.chat.completions.create({
        model: "gpt-4-vision-preview", // Fallback to vision preview model
        messages: [
          {
            role: "system",
            content:
              "أنت حكيمة روحانية قديمة، تحمل في قلبك أسرار الكون وحكمة الأجداد. عندك القدرة على رؤية ما وراء الحجب، وقراءة الرموز التي تتكلم بلغة الروح. عندما تنظر إلى فنجان القهوة، أنت لا ترى مجرد أشكال، بل ترى قصص القلوب، وأحلام النفوس، ومسارات الأقدار. تكلم بلغة تلامس الوجدان، وكأن كلماتك نسمات دافئة تداعب الأرواح. اربط كل رمز بالمشاعر الإنسانية العميقة، بالحب الذي يحرك القلوب، بالأحلام التي تضيء الطريق، بالتحولات التي تشكل المصائر. دع كل من يقرأ كلماتك يشعر أن الرسالة موجهة إليه شخصياً، وأن الكون يتحدث معه من خلالك. استخدم لغة عربية شاعرية وروحانية، تجعل القارئ يشعر بالدفء والأمان والحكمة.",
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: prompt,
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/${imageFormat};base64,${base64Image}`,
                  detail: "high",
                },
              },
            ],
          },
        ],
        max_tokens: 6000,
        temperature: 0.9,
      });
    }

    console.log(
      "OpenAI response received:",
      response.choices[0]?.message?.content?.substring(0, 200) + "...",
    );

    const responseContent = response.choices[0]?.message?.content;

    // Check if the response indicates inability to see images
    if (
      responseContent &&
      (responseContent.includes("لا أستطيع تحديد") ||
        responseContent.includes("لا أستطيع رؤية") ||
        responseContent.includes("لا أستطيع تحليل الصور") ||
        responseContent.includes("cannot see") ||
        responseContent.includes("cannot analyze"))
    ) {
      console.warn(
        "Model claims it cannot see images, this might be a prompt issue",
      );
      // Try a different approach with a more direct prompt
      return await retryWithDirectPrompt(imageFormat, base64Image, userMessage);
    }

    return (
      responseContent ||
      "عذراً، لم أتمكن من تحليل الصورة. يرجى المحاولة مرة أخرى."
    );
  } catch (error) {
    console.error("Error analyzing coffee image:", error);

    // More detailed error logging
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }

    // Check if it's an API key issue
    if (error instanceof Error && error.message.includes("API key")) {
      throw new Error("مشكلة في مفتاح API. يرجى التحقق من إعدادات OpenAI.");
    }

    // Check if it's a model access issue
    if (error instanceof Error && error.message.includes("model")) {
      throw new Error(
        "مشكلة في الوصول إلى نموذج GPT-4o. يرجى التحقق من حسابك في OpenAI.",
      );
    }

    throw new Error(
      `فشل في تحليل صورة الفنجان: ${
        error instanceof Error ? error.message : "خطأ غير معروف"
      }`,
    );
  }
}

// Retry function with a more direct prompt
async function retryWithDirectPrompt(
  imageFormat: string,
  base64Image: string,
  userMessage?: string,
) {
  console.log("Retrying with direct prompt approach...");

  const directPrompt = userMessage
    ? `أعطني قراءة روحانية عميقة لفنجان قهوة تركية بناءً على الصورة التي سأرسلها. اجعل الإجابة موجهة بشكل عام لأي شخص ينظر في هذا الفنجان، وكأنك حكيمة قديمة تقرأ للناس من خلال الرموز. ${userMessage}. اربط الرموز الموجودة في الفنجان بالمشاعر، بالتجارب الإنسانية، بالحب، بالقرارات الصعبة، بالتحولات. اجعل القراءة تمس القارئ وكأنك تخاطب قلبه، لا عقله فقط. استخدم لغة عربية حساسة وعاطفية، بنغمة روحية، تجذب الانتباه وتوقظ الذاكرة والمشاعر. دع القارئ يشعر أن الرسالة له هو، حتى وإن لم يذكر اسمه. ابتعد عن الجمل الجافة، واجعل الكلام مثل نَفَسٍ دافئ يصل إلى الداخل.`
    : `أعطني قراءة روحانية عميقة لفنجان قهوة تركية بناءً على الصورة التي سأرسلها. اجعل الإجابة موجهة بشكل عام لأي شخص ينظر في هذا الفنجان، وكأنك حكيمة قديمة تقرأ للناس من خلال الرموز. اربط الرموز الموجودة في الفنجان بالمشاعر، بالتجارب الإنسانية، بالحب، بالقرارات الصعبة، بالتحولات. اجعل القراءة تمس القارئ وكأنك تخاطب قلبه، لا عقله فقط. استخدم لغة عربية حساسة وعاطفية، بنغمة روحية، تجذب الانتباه وتوقظ الذاكرة والمشاعر. دع القارئ يشعر أن الرسالة له هو، حتى وإن لم يذكر اسمه. ابتعد عن الجمل الجافة، واجعل الكلام مثل نَفَسٍ دافئ يصل إلى الداخل.`;

  const response = await openai.chat.completions.create({
    model: "gpt-4o",
    messages: [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: directPrompt,
          },
          {
            type: "image_url",
            image_url: {
              url: `data:image/${imageFormat};base64,${base64Image}`,
              detail: "high",
            },
          },
        ],
      },
    ],
    max_tokens: 6000,
    temperature: 0.9,
  });

  return (
    response.choices[0]?.message?.content ||
    "عذراً، لم أتمكن من تحليل الصورة. يرجى المحاولة مرة أخرى."
  );
}
